# Dreamy Design System
*A design language crafted for mental tranquility and visual bliss*

## Philosophy

> "Simplicity is the ultimate sophistication. Every pixel serves the human soul."

This design system is built on the principle that technology should calm, not overwhelm. Inspired by <PERSON><PERSON>'s philosophy of pure minimalism, every element is designed to reduce cognitive load and promote mental peace.

### Core Principles

1. **Zero Border Radius** - Pure geometric forms, no artificial curves
2. **No Gradients** - Only solid, breathing colors
3. **Soft Shadows** - Depth without weight
4. **Generous Whitespace** - Room for thoughts to breathe
5. **Fluid Animations** - Movements that mirror nature

## Color Palette

Our colors are chosen to evoke specific emotional states, each serving the user's mental well-being.

### Primary Colors

```css
--deep-ocean: #000072      /* Foundation of all thought */
--violet-embrace: #A536E0  /* Warm, nurturing, creative */
--plum-serenity: #6E36E0   /* Balanced, grounded, wise */
--sky-breath: #366CE0      /* Infinite possibility, clarity */
--magenta-dream: #DD36E0   /* Energy, life, gentle passion */
--lavender-mist: #A081E0   /* Creativity, imagination, peace */
```

### Semantic Mapping

- **Primary Actions**: Sky Breath (#366CE0) - Clarity and possibility
- **Secondary Actions**: Plum Serenity (#6E36E0) - Wisdom and balance
- **Accent Elements**: Violet Embrace (#A536E0) - Warmth and creativity
- **Warnings**: Magenta Dream (#DD36E0) - Gentle attention
- **Text**: Deep Ocean (#000072) - Grounded and readable

## Typography

### Scale
```css
--text-whisper: 0.75rem    /* 12px - Subtle annotations */
--text-breath: 0.875rem    /* 14px - Supporting text */
--text-flow: 1rem          /* 16px - Body text */
--text-wave: 1.125rem      /* 18px - Emphasized text */
--text-tide: 1.25rem       /* 20px - Small headings */
--text-horizon: 1.5rem     /* 24px - Section headings */
--text-sky: 2rem           /* 32px - Page headings */
--text-cosmos: 3rem        /* 48px - Hero text */
```

### Font Stack
```css
font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
```

## Spacing System

Our spacing follows natural rhythms, like breathing or heartbeats.

```css
--space-atom: 0.125rem     /* 2px - The smallest breath */
--space-whisper: 0.25rem   /* 4px - A gentle touch */
--space-breath: 0.5rem     /* 8px - Natural rhythm */
--space-pulse: 0.75rem     /* 12px - Heartbeat */
--space-flow: 1rem         /* 16px - Natural flow */
--space-wave: 1.5rem       /* 24px - Gentle wave */
--space-tide: 2rem         /* 32px - Ocean tide */
--space-horizon: 3rem      /* 48px - Distant horizon */
--space-sky: 4rem          /* 64px - Open sky */
--space-cosmos: 6rem       /* 96px - Infinite space */
```

## Shadows

Shadows that feel like gentle mist, never harsh or imposing.

```css
--shadow-whisper: 0 1px 3px 0 rgba(0, 0, 114, 0.03)
--shadow-breath: 0 2px 8px 0 rgba(0, 0, 114, 0.04)
--shadow-float: 0 4px 16px 0 rgba(0, 0, 114, 0.06)
--shadow-dream: 0 8px 32px 0 rgba(0, 0, 114, 0.08)
--shadow-transcend: 0 16px 64px 0 rgba(0, 0, 114, 0.1)
```

## Animation

### Timing
```css
--timing-instant: 0ms
--timing-breath: 150ms     /* Quick responses */
--timing-heartbeat: 300ms  /* Standard interactions */
--timing-wave: 500ms       /* Gentle transitions */
--timing-tide: 800ms       /* Thoughtful changes */
--timing-dream: 1200ms     /* Contemplative movements */
```

### Easing
```css
--ease-breath: cubic-bezier(0.4, 0, 0.2, 1)      /* Natural acceleration */
--ease-float: cubic-bezier(0.25, 0.46, 0.45, 0.94) /* Gentle floating */
--ease-dream: cubic-bezier(0.23, 1, 0.32, 1)     /* Dreamy smoothness */
```

## Components

### Cards
```css
.card-dream     /* Primary cards with full presence */
.card-whisper   /* Subtle cards for secondary content */
.card-float     /* Elevated cards for important content */
```

### Buttons
```css
.btn-dream      /* Primary actions - sky breath */
.btn-whisper    /* Secondary actions - plum serenity */
.btn-ghost      /* Tertiary actions - transparent */
.btn-accent     /* Special actions - rose embrace */
```

### Inputs
```css
.input-dream    /* Primary form inputs */
.input-whisper  /* Subtle form inputs */
```

### Layout
```css
.container-dream   /* Main content container (1200px) */
.container-whisper /* Narrow content container (800px) */
.section-dream     /* Major page sections */
.section-whisper   /* Minor page sections */
```

## Usage Guidelines

### Do's
- Use generous whitespace to let content breathe
- Apply consistent spacing from our scale
- Layer shadows subtly to create depth
- Animate with natural timing and easing
- Choose colors based on emotional intent

### Don'ts
- Never add border-radius to any element
- Avoid gradients or complex color transitions
- Don't use harsh shadows or high contrast
- Never rush animations or use linear easing
- Avoid cramped layouts or tight spacing

## Dark Mode

Dark mode maintains the same peaceful philosophy with deeper, more restful colors:

- Background becomes deep night sky (#0a0a0f)
- Text becomes soft moonlight (rgba(255, 255, 255, 0.9))
- Cards float with subtle transparency
- Shadows become deeper but remain gentle

## Accessibility

- All colors meet WCAG AA contrast requirements
- Focus states are clearly visible with soft rings
- Animations respect prefers-reduced-motion
- Touch targets are minimum 44px
- Text scales appropriately with user preferences

---

*"In the end, we believe that design is about more than just aesthetics. It's about creating experiences that honor the human spirit and promote well-being. Every choice we make should serve that higher purpose."*
