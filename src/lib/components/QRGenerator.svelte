<script lang="ts">
  import { onMount } from 'svelte';
  import QrCodeWithLogo from 'qrcode-with-logos';

  export let data: string;
  export let size: number = 380;

  let containerElement: HTMLDivElement;
  let qrcode: QrCodeWithLogo;
  let isGenerating = false;

  onMount(() => {
    generateQR();
  });

  $: if (data) {
    generateQR();
  }

  async function generateQR() {
    if (!data || !containerElement) return;

    isGenerating = true;

    try {
      // Clear previous QR code
      containerElement.innerHTML = '';

      // Create an image element for the QR code (required by qrcode-with-logos)
      const img = document.createElement('img');
      img.id = 'qr-image';
      containerElement.appendChild(img);

      // Create QR code with logo
      qrcode = new QrCodeWithLogo({
        content: data,
        width: size,
        image: img,
        logo: {
          src: '/logo.jpg'
        }
      });

    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      isGenerating = false;
    }
  }

  async function copyToClipboard() {
    try {
      if (qrcode && navigator.clipboard) {
        // Get the canvas from the QR code (qrcode-with-logos creates a canvas internally)
        const canvas = containerElement.querySelector('canvas');
        if (canvas) {
          const dataUrl = canvas.toDataURL('image/png');
          const response = await fetch(dataUrl);
          const blob = await response.blob();

          await navigator.clipboard.write([
            new ClipboardItem({ 'image/png': blob })
          ]);

          // Dispatch success event
          const event = new CustomEvent('copy-success');
          containerElement?.dispatchEvent(event);
        } else {
          // Fallback: try to get the image src
          const img = containerElement.querySelector('img');
          if (img && img.src) {
            const response = await fetch(img.src);
            const blob = await response.blob();

            await navigator.clipboard.write([
              new ClipboardItem({ 'image/png': blob })
            ]);

            const event = new CustomEvent('copy-success');
            containerElement?.dispatchEvent(event);
          }
        }
      } else {
        // Fallback: copy the data text
        await navigator.clipboard.writeText(data);
        const event = new CustomEvent('copy-success');
        containerElement?.dispatchEvent(event);
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      // Dispatch error event
      const event = new CustomEvent('copy-error', { detail: error });
      containerElement?.dispatchEvent(event);
    }
  }

  function downloadQR() {
    if (!qrcode) return;

    try {
      // Use the built-in download method
      qrcode.downloadImage('qrcode.png');

      // Dispatch download event
      const event = new CustomEvent('download-success');
      containerElement?.dispatchEvent(event);
    } catch (error) {
      console.error('Error downloading QR code:', error);
    }
  }
</script>

<div class="qr-generator">
  {#if isGenerating}
    <div class="loading">
      <div class="spinner"></div>
      <p>Generating QR code...</p>
    </div>
  {:else}
    <div class="qr-display">
      <div
        bind:this={containerElement}
        class="qr-container"
      ></div>
    </div>

    <div class="qr-actions">
      <button
        class="action-btn copy-btn"
        on:click={copyToClipboard}
        title="Copy QR code to clipboard"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
        Copy
      </button>

      <button
        class="action-btn download-btn"
        on:click={downloadQR}
        title="Download QR code as PNG"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="7,10 12,15 17,10"></polyline>
          <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        Download
      </button>
    </div>

    <div class="qr-info">
      <p class="data-preview">Data: <code>{data}</code></p>
    </div>
  {/if}
</div>

<style>
  .qr-generator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
  }

  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .qr-display {
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .qr-container {
    display: block;
    border-radius: 0.25rem;
  }

  .qr-actions {
    display: flex;
    gap: 0.5rem;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s;
  }

  .copy-btn {
    background: var(--color-secondary);
    color: var(--color-secondary-foreground);
  }

  .copy-btn:hover {
    background: var(--color-secondary);
    opacity: 0.9;
  }

  .download-btn {
    background: var(--color-primary);
    color: var(--color-primary-foreground);
  }

  .download-btn:hover {
    background: var(--color-primary);
    opacity: 0.9;
  }

  .qr-info {
    max-width: 300px;
    text-align: center;
  }

  .data-preview {
    font-size: 0.8rem;
    color: var(--color-muted-foreground);
    word-break: break-all;
  }

  .data-preview code {
    background: var(--color-muted);
    padding: 0.2rem 0.4rem;
    border-radius: 0.2rem;
    font-family: monospace;
    font-size: 0.75rem;
  }
</style>
