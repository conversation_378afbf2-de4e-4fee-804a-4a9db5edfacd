<script lang="ts">
  import { onMount } from 'svelte';
  import { scale, fade } from 'svelte/transition';
  
  let mounted = false;
  let activeTab = 'colors';
  
  onMount(() => {
    mounted = true;
  });
  
  const tabs = [
    { id: 'colors', label: 'Colors' },
    { id: 'typography', label: 'Typography' },
    { id: 'components', label: 'Components' },
    { id: 'spacing', label: 'Spacing' },
    { id: 'shadows', label: 'Shadows' }
  ];
  
  const colors = [
    { name: 'Deep Ocean', var: '--deep-ocean', hex: '#000072', desc: 'Foundation of all thought' },
    { name: 'Violet Embrace', var: '--violet-embrace', hex: '#A536E0', desc: 'Warm, nurturing, creative' },
    { name: 'Plum Serenity', var: '--plum-serenity', hex: '#6E36E0', desc: 'Balanced, grounded, wise' },
    { name: 'Sky Breath', var: '--sky-breath', hex: '#366CE0', desc: 'Infinite possibility, clarity' },
    { name: 'Magenta Dream', var: '--magenta-dream', hex: '#DD36E0', desc: 'Energy, life, gentle passion' },
    { name: 'Lavender Mist', var: '--lavender-mist', hex: '#A081E0', desc: 'Creativity, imagination, peace' }
  ];
</script>

<svelte:head>
  <title>Dreamy Design System</title>
</svelte:head>

<div class="container-dream">
  {#if mounted}
    <header class="section-whisper text-center" in:fade={{ duration: 800 }}>
      <h1 class="text-cosmos mb-4" style="color: var(--deep-ocean);">Dreamy Design System</h1>
      <p class="text-wave text-whisper max-w-2xl mx-auto">
        A design language crafted for mental tranquility and visual bliss. 
        Every element designed to reduce cognitive load and promote peace.
      </p>
    </header>

    <!-- Navigation Tabs -->
    <nav class="flex justify-center mb-8" in:scale={{ delay: 200, duration: 600 }}>
      <div class="card-whisper p-1 inline-flex">
        {#each tabs as tab, i}
          <button
            class="px-6 py-3 text-flow font-medium transition-breath {activeTab === tab.id ? 'btn-dream' : 'btn-ghost'}"
            on:click={() => activeTab = tab.id}
            in:scale={{ delay: 300 + i * 50, duration: 400 }}
          >
            {tab.label}
          </button>
        {/each}
      </div>
    </nav>

    <!-- Content Sections -->
    <main class="section-dream">
      {#if activeTab === 'colors'}
        <section in:fade={{ duration: 500 }}>
          <h2 class="text-sky mb-8 text-center">Color Palette</h2>
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {#each colors as color, i}
              <div class="card-dream hover-lift transition-float" in:scale={{ delay: i * 100, duration: 500 }}>
                <div 
                  class="w-full h-24 mb-4" 
                  style="background-color: {color.hex};"
                ></div>
                <h3 class="text-tide font-semibold mb-2">{color.name}</h3>
                <p class="text-breath text-whisper mb-2">{color.desc}</p>
                <code class="text-whisper font-mono">{color.hex}</code>
              </div>
            {/each}
          </div>
        </section>
      {/if}

      {#if activeTab === 'typography'}
        <section in:fade={{ duration: 500 }}>
          <h2 class="text-sky mb-8 text-center">Typography Scale</h2>
          <div class="card-dream space-dream">
            <h1 class="text-cosmos">Cosmos - 48px</h1>
            <h2 class="text-sky">Sky - 32px</h2>
            <h3 class="text-horizon">Horizon - 24px</h3>
            <h4 class="text-tide">Tide - 20px</h4>
            <h5 class="text-wave">Wave - 18px</h5>
            <h6 class="text-flow">Flow - 16px</h6>
            <p class="text-breath">Breath - 14px</p>
            <small class="text-whisper">Whisper - 12px</small>
          </div>
        </section>
      {/if}

      {#if activeTab === 'components'}
        <section in:fade={{ duration: 500 }}>
          <h2 class="text-sky mb-8 text-center">Components</h2>
          
          <div class="space-dream">
            <!-- Buttons -->
            <div class="card-dream">
              <h3 class="text-horizon mb-6">Buttons</h3>
              <div class="flex flex-wrap gap-4">
                <button class="btn-dream">Primary Action</button>
                <button class="btn-whisper">Secondary Action</button>
                <button class="btn-ghost">Ghost Action</button>
                <button class="btn-accent">Accent Action</button>
              </div>
            </div>

            <!-- Cards -->
            <div>
              <h3 class="text-horizon mb-6">Cards</h3>
              <div class="grid md:grid-cols-3 gap-6">
                <div class="card-whisper">
                  <h4 class="text-tide mb-2">Whisper Card</h4>
                  <p class="text-breath text-whisper">Subtle and gentle</p>
                </div>
                <div class="card-dream">
                  <h4 class="text-tide mb-2">Dream Card</h4>
                  <p class="text-breath text-whisper">Primary content</p>
                </div>
                <div class="card-float">
                  <h4 class="text-tide mb-2">Float Card</h4>
                  <p class="text-breath text-whisper">Elevated importance</p>
                </div>
              </div>
            </div>

            <!-- Inputs -->
            <div class="card-dream">
              <h3 class="text-horizon mb-6">Form Elements</h3>
              <div class="space-whisper max-w-md">
                <input class="input-dream" placeholder="Dream input field" />
                <input class="input-whisper" placeholder="Whisper input field" />
              </div>
            </div>
          </div>
        </section>
      {/if}

      {#if activeTab === 'spacing'}
        <section in:fade={{ duration: 500 }}>
          <h2 class="text-sky mb-8 text-center">Spacing System</h2>
          <div class="card-dream">
            <div class="space-y-4">
              <div class="flex items-center gap-4">
                <div class="w-2 h-8 bg-primary"></div>
                <span>atom - 2px</span>
              </div>
              <div class="flex items-center gap-4">
                <div class="w-4 h-8 bg-primary"></div>
                <span>whisper - 4px</span>
              </div>
              <div class="flex items-center gap-4">
                <div class="w-8 h-8 bg-primary"></div>
                <span>breath - 8px</span>
              </div>
              <div class="flex items-center gap-4">
                <div class="w-12 h-8 bg-primary"></div>
                <span>pulse - 12px</span>
              </div>
              <div class="flex items-center gap-4">
                <div class="w-16 h-8 bg-primary"></div>
                <span>flow - 16px</span>
              </div>
              <div class="flex items-center gap-4">
                <div class="w-24 h-8 bg-primary"></div>
                <span>wave - 24px</span>
              </div>
              <div class="flex items-center gap-4">
                <div class="w-32 h-8 bg-primary"></div>
                <span>tide - 32px</span>
              </div>
            </div>
          </div>
        </section>
      {/if}

      {#if activeTab === 'shadows'}
        <section in:fade={{ duration: 500 }}>
          <h2 class="text-sky mb-8 text-center">Shadow Depths</h2>
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="card-whisper shadow-whisper p-8 text-center">
              <h3 class="text-tide mb-2">Whisper</h3>
              <p class="text-breath text-whisper">Barely there</p>
            </div>
            <div class="card-whisper shadow-breath p-8 text-center">
              <h3 class="text-tide mb-2">Breath</h3>
              <p class="text-breath text-whisper">Gentle presence</p>
            </div>
            <div class="card-whisper shadow-float p-8 text-center">
              <h3 class="text-tide mb-2">Float</h3>
              <p class="text-breath text-whisper">Lifted softly</p>
            </div>
            <div class="card-whisper shadow-dream p-8 text-center">
              <h3 class="text-tide mb-2">Dream</h3>
              <p class="text-breath text-whisper">Floating freely</p>
            </div>
            <div class="card-whisper shadow-transcend p-8 text-center">
              <h3 class="text-tide mb-2">Transcend</h3>
              <p class="text-breath text-whisper">Beyond reality</p>
            </div>
          </div>
        </section>
      {/if}
    </main>
  {/if}
</div>

<style>
  /* Component-specific styles */
  .text-whisper {
    color: var(--muted-foreground);
  }
</style>
