<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  function navigateToSignIn() {
    goto(`/sc/${$page.params.id}/p/i`);
  }

  function navigateToSignOut() {
    goto(`/sc/${$page.params.id}/p/o`);
  }

  function goBack() {
    goto(`/sc/${$page.params.id}`);
  }
</script>

<svelte:head>
  <title>Presence Management - {data.school?.n || 'School'}</title>
</svelte:head>

<div class="presence-dashboard">
  <div class="header">
    <button class="btn-soft-ghost" on:click={goBack}>
      ← Back to School
    </button>
    <div class="header-content">
      <h1>Presence Management</h1>
      <h2>{data.school?.n || 'School'}</h2>
      <p>Manage student and staff attendance</p>
    </div>
  </div>

  <div class="content">
    <div class="dashboard-grid">
      <!-- Sign In Card -->
      <div class="action-card sign-in-card">
        <div class="card-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
            <polyline points="10,17 15,12 10,7"></polyline>
            <line x1="15" y1="12" x2="3" y2="12"></line>
          </svg>
        </div>
        <div class="card-content">
          <h3>Sign Users In</h3>
          <p>Scan QR codes to record student and staff arrivals</p>
          <button class="action-btn sign-in-btn" on:click={navigateToSignIn}>
            Start Sign In
          </button>
        </div>
      </div>

      <!-- Sign Out Card -->
      <div class="action-card sign-out-card">
        <div class="card-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
            <polyline points="16,17 21,12 16,7"></polyline>
            <line x1="21" y1="12" x2="9" y2="12"></line>
          </svg>
        </div>
        <div class="card-content">
          <h3>Sign Users Out</h3>
          <p>Scan QR codes to record student and staff departures</p>
          <button class="action-btn sign-out-btn" on:click={navigateToSignOut}>
            Start Sign Out
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-section">
      <h3>Today's Overview</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.totalUsers || 0}</div>
            <div class="stat-label">Total Users</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
              <polyline points="10,17 15,12 10,7"></polyline>
              <line x1="15" y1="12" x2="3" y2="12"></line>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.signedIn || 0}</div>
            <div class="stat-label">Signed In Today</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16,17 21,12 16,7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.signedOut || 0}</div>
            <div class="stat-label">Signed Out Today</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.currentlyPresent || 0}</div>
            <div class="stat-label">Currently Present</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Instructions -->
    <div class="instructions-section">
      <h3>How to Use</h3>
      <div class="instructions-grid">
        <div class="instruction-item">
          <div class="instruction-number">1</div>
          <div class="instruction-content">
            <h4>Choose Action</h4>
            <p>Select "Sign Users In" for arrivals or "Sign Users Out" for departures</p>
          </div>
        </div>

        <div class="instruction-item">
          <div class="instruction-number">2</div>
          <div class="instruction-content">
            <h4>Scan QR Codes</h4>
            <p>Use your device camera to scan student or staff QR codes</p>
          </div>
        </div>

        <div class="instruction-item">
          <div class="instruction-number">3</div>
          <div class="instruction-content">
            <h4>Automatic Recording</h4>
            <p>Attendance is automatically recorded with timestamp and validation</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .presence-dashboard {
    min-height: 100vh;
    padding: var(--space-lg);
  }

  .header {
    margin-bottom: var(--space-2xl);
  }

  .header-content {
    text-align: center;
    margin-top: var(--space-xl);
  }

  .header h1 {
    font-size: 2.5rem;
    margin: 0 0 var(--space-sm) 0;
    font-weight: 300;
    color: var(--foreground);
  }

  .header h2 {
    font-size: 1.5rem;
    margin: 0 0 var(--space-sm) 0;
    font-weight: 500;
    color: var(--foreground);
  }

  .header p {
    font-size: 1rem;
    margin: 0;
    color: var(--muted-foreground);
  }

  .content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-3xl);
  }

  .action-card {
    background: var(--card);
    border-radius: var(--radius-card);
    padding: var(--space-xl);
    text-align: center;
    box-shadow: var(--shadow-soft-lg);
    border: 1px solid var(--border);
    transition: all 0.2s;
  }

  .action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-soft-xl);
  }

  .card-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-card);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg) auto;
    box-shadow: var(--shadow-soft);
  }

  .sign-in-card .card-icon {
    background: #22c55e;
    color: white;
  }

  .sign-out-card .card-icon {
    background: var(--destructive);
    color: var(--destructive-foreground);
  }

  .card-content h3 {
    font-size: 1.5rem;
    margin: 0 0 var(--space-md) 0;
    color: var(--foreground);
    font-weight: 500;
  }

  .card-content p {
    color: var(--muted-foreground);
    margin: 0 0 var(--space-xl) 0;
    line-height: 1.5;
  }

  .action-btn {
    padding: var(--space-lg) var(--space-xl);
    border: none;
    border-radius: var(--radius-button);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    width: 100%;
  }

  .sign-in-btn {
    background: #22c55e;
    color: white;
  }

  .sign-out-btn {
    background: var(--destructive);
    color: var(--destructive-foreground);
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
    opacity: 0.9;
  }

  .stats-section,
  .instructions-section {
    background: var(--card);
    border-radius: var(--radius-card);
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--border);
  }

  .stats-section h3,
  .instructions-section h3 {
    font-size: 1.5rem;
    margin: 0 0 var(--space-lg) 0;
    color: var(--foreground);
    text-align: center;
    font-weight: 500;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-card);
    transition: all 0.2s;
    background: var(--muted);
  }

  .stat-card:hover {
    border-color: var(--primary);
    transform: translateY(-2px);
    background: var(--background);
    box-shadow: var(--shadow-soft);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    background: var(--primary);
    color: var(--primary-foreground);
    border-radius: var(--radius-card);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: var(--shadow-soft);
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--foreground);
  }

  .stat-label {
    font-size: 0.9rem;
    color: var(--muted-foreground);
  }

  .instructions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
  }

  .instruction-item {
    display: flex;
    gap: var(--space-md);
    align-items: flex-start;
  }

  .instruction-number {
    width: 32px;
    height: 32px;
    background: var(--primary);
    color: var(--primary-foreground);
    border-radius: var(--radius-card);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
    box-shadow: var(--shadow-soft);
  }

  .instruction-content h4 {
    font-size: 1.1rem;
    margin: 0 0 var(--space-sm) 0;
    color: var(--foreground);
    font-weight: 500;
  }

  .instruction-content p {
    font-size: 0.9rem;
    color: var(--muted-foreground);
    margin: 0;
    line-height: 1.4;
  }

  @media (max-width: 640px) {
    .presence-dashboard {
      padding: var(--space-md);
    }

    .header {
      margin-bottom: var(--space-xl);
    }

    .header h1 {
      font-size: 2rem;
    }

    .dashboard-grid {
      grid-template-columns: 1fr;
      gap: var(--space-lg);
      margin-bottom: var(--space-xl);
    }

    .action-card {
      padding: var(--space-lg);
    }

    .stats-section,
    .instructions-section {
      padding: var(--space-lg);
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .instructions-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
